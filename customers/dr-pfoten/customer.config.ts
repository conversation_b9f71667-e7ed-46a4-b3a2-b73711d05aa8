import type { CustomerConfig } from '../types';
import { PortalFeature } from '../../src/app/core/types';

export const customerConfig: CustomerConfig = {
  id: 'dr-pfoten',
  name: '<PERSON><PERSON> <PERSON><PERSON><PERSON>',
  supabase: {
    url: 'https://jthdagdrrxcyfsulrnql.supabase.co',
    anonKey:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.FUogH3xIT8fnuAiVLnT1y6CsD_RY__EknRE4XRFVhHs',
  },
  enabledFeatures: [
    PortalFeature.FB_ACCOUNTS_MANAGER,
    PortalFeature.AD_WINNERS,
    PortalFeature.CREATIVES_UPLOADER,
  ],
  n8nBaseUrl: 'https://n8n.pfotendash.de',
};
